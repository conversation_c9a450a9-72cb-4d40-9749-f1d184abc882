<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="utf-8">
  <meta content="width=device-width, initial-scale=1.0" name="viewport">
  <title>马到码成 - Java毕业项目源代码销售平台</title>
  <meta name="description" content="马到码成(MDMC) - 专业的Java毕业项目源代码销售平台，提供100%可运行的毕业设计项目，助力学生顺利毕业">
  <meta name="keywords" content="Java毕业设计,毕业项目源代码,Java项目,毕业设计代码,学生项目,源码销售">

  <!-- Favicons -->
  <link href="assets/img/favicon.png" rel="icon">
  <link href="assets/img/apple-touch-icon.png" rel="apple-touch-icon">

  <!-- Fonts -->
  <link href="https://fonts.googleapis.com" rel="preconnect">
  <link href="https://fonts.gstatic.com" rel="preconnect" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&family=Inter:wght@100;200;300;400;500;600;700;800;900&family=Nunito:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">

  <!-- Vendor CSS Files -->
  <link href="assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
  <link href="assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
  <link href="assets/vendor/aos/aos.css" rel="stylesheet">
  <link href="assets/vendor/glightbox/css/glightbox.min.css" rel="stylesheet">
  <link href="assets/vendor/swiper/swiper-bundle.min.css" rel="stylesheet">

  <!-- Main CSS File -->
  <link href="assets/css/main.css" rel="stylesheet">

  <!-- =======================================================
  * 模板名称: iLanding
  * 模板链接: https://bootstrapmade.com/ilanding-bootstrap-landing-page-template/
  * 更新时间: 2024年11月12日，使用 Bootstrap v5.3.3
  * 作者: BootstrapMade.com
  * 许可证: https://bootstrapmade.com/license/
  ======================================================== -->
</head>

<body class="index-page">

  <header id="header" class="header d-flex align-items-center fixed-top">
    <div class="header-container container-fluid container-xl position-relative d-flex align-items-center justify-content-between">

      <a href="index.html" class="logo d-flex align-items-center me-auto me-xl-0">
        <!-- 如果您也希望使用图片logo，请取消下面一行的注释 -->
        <!-- <img src="assets/img/logo.png" alt=""> -->
        <h1 class="sitename">马到码成</h1>
      </a>

      <nav id="navmenu" class="navmenu">
        <ul>
          <li><a href="#hero" class="active">首页</a></li>
          <li><a href="#about">关于我们</a></li>
          <li><a href="#features">项目特色</a></li>
          <li><a href="#services">项目分类</a></li>
          <li><a href="#pricing">价格方案</a></li>
          <li class="dropdown"><a href="#"><span>项目展示</span> <i class="bi bi-chevron-down toggle-dropdown"></i></a>
            <ul>
              <li><a href="#">管理系统</a></li>
              <li class="dropdown"><a href="#"><span>电商平台</span> <i class="bi bi-chevron-down toggle-dropdown"></i></a>
                <ul>
                  <li><a href="#">在线商城</a></li>
                  <li><a href="#">购物车系统</a></li>
                  <li><a href="#">支付系统</a></li>
                  <li><a href="#">订单管理</a></li>
                  <li><a href="#">用户中心</a></li>
                </ul>
              </li>
              <li><a href="#">小程序项目</a></li>
              <li><a href="#">Web应用</a></li>
              <li><a href="#">移动应用</a></li>
            </ul>
          </li>
          <li><a href="#contact">联系我们</a></li>
        </ul>
        <i class="mobile-nav-toggle d-xl-none bi bi-list"></i>
      </nav>

      <a class="btn-getstarted" href="index.html#about">选购项目</a>

    </div>
  </header>

  <main class="main">

    <!-- 主页横幅区域 -->
    <section id="hero" class="hero section">

      <div class="container" data-aos="fade-up" data-aos-delay="100">

        <div class="row align-items-center">
          <div class="col-lg-6">
            <div class="hero-content" data-aos="fade-up" data-aos-delay="200">
              <div class="company-badge mb-4">
                <i class="bi bi-gear-fill me-2"></i>
                马到码成，助您毕业成功
              </div>

              <h1 class="mb-4">
                100%可运行 <br>
                完整文档 <br>
                <span class="accent-text">Java毕业项目</span>
              </h1>

              <p class="mb-4 mb-md-5">
                专业提供Java毕业设计项目源代码，所有项目均经过团队测试，
                100%可运行无错误，配套完整文档和答辩材料，助您顺利毕业。
              </p>

              <div class="hero-buttons">
                <a href="#about" class="btn btn-primary me-0 me-sm-2 mx-1">立即选购</a>
                <a href="https://www.youtube.com/watch?v=Y7f98aduVJ8" class="btn btn-link mt-2 mt-sm-0 glightbox">
                  <i class="bi bi-play-circle me-1"></i>
                  项目演示
                </a>
              </div>
            </div>
          </div>

          <div class="col-lg-6">
            <div class="hero-image" data-aos="zoom-out" data-aos-delay="300">
              <img src="assets/img/illustration-1.webp" alt="主页插图" class="img-fluid">

              <div class="customers-badge">
                <div class="customer-avatars">
                  <img src="assets/img/avatar-1.webp" alt="客户 1" class="avatar">
                  <img src="assets/img/avatar-2.webp" alt="客户 2" class="avatar">
                  <img src="assets/img/avatar-3.webp" alt="客户 3" class="avatar">
                  <img src="assets/img/avatar-4.webp" alt="客户 4" class="avatar">
                  <img src="assets/img/avatar-5.webp" alt="客户 5" class="avatar">
                  <span class="avatar more">12+</span>
                </div>
                <p class="mb-0 mt-2">12,000+ 学生成功毕业，好评如潮</p>
              </div>
            </div>
          </div>
        </div>

        <div class="row stats-row gy-4 mt-5" data-aos="fade-up" data-aos-delay="500">
          <div class="col-lg-3 col-md-6">
            <div class="stat-item">
              <div class="stat-icon">
                <i class="bi bi-trophy"></i>
              </div>
              <div class="stat-content">
                <h4>500+ 项目案例</h4>
                <p class="mb-0">丰富选择</p>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6">
            <div class="stat-item">
              <div class="stat-icon">
                <i class="bi bi-briefcase"></i>
              </div>
              <div class="stat-content">
                <h4>100% 可运行</h4>
                <p class="mb-0">质量保证</p>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6">
            <div class="stat-item">
              <div class="stat-icon">
                <i class="bi bi-graph-up"></i>
              </div>
              <div class="stat-content">
                <h4>98% 毕业成功率</h4>
                <p class="mb-0">信赖之选</p>
              </div>
            </div>
          </div>
          <div class="col-lg-3 col-md-6">
            <div class="stat-item">
              <div class="stat-icon">
                <i class="bi bi-award"></i>
              </div>
              <div class="stat-content">
                <h4>5年 服务经验</h4>
                <p class="mb-0">专业保障</p>
              </div>
            </div>
          </div>
        </div>

      </div>

    </section><!-- /主页横幅区域 -->

    <!-- 关于我们区域 -->
    <section id="about" class="about section">

      <div class="container" data-aos="fade-up" data-aos-delay="100">

        <div class="row gy-4 align-items-center justify-content-between">

          <div class="col-xl-5" data-aos="fade-up" data-aos-delay="200">
            <span class="about-meta">了解更多</span>
            <h2 class="about-title">马到码成，专业Java毕业项目</h2>
            <p class="about-description">我们专注于Java毕业设计项目源代码销售，拥有丰富的项目案例和专业的技术团队。所有项目均经过严格测试，确保100%可运行，配套完整的文档资料和答辩材料，助力学生顺利完成毕业设计。</p>

            <div class="row feature-list-wrapper">
              <div class="col-md-6">
                <ul class="feature-list">
                  <li><i class="bi bi-check-circle-fill"></i> 100%可运行代码</li>
                  <li><i class="bi bi-check-circle-fill"></i> 完整项目文档</li>
                  <li><i class="bi bi-check-circle-fill"></i> 数据库文件齐全</li>
                </ul>
              </div>
              <div class="col-md-6">
                <ul class="feature-list">
                  <li><i class="bi bi-check-circle-fill"></i> PPT答辩材料</li>
                  <li><i class="bi bi-check-circle-fill"></i> 技术问答支持</li>
                  <li><i class="bi bi-check-circle-fill"></i> 定制开发服务</li>
                </ul>
              </div>
            </div>

            <div class="info-wrapper">
              <div class="row gy-4">
                <div class="col-lg-5">
                  <div class="profile d-flex align-items-center gap-3">
                    <img src="assets/img/avatar-1.webp" alt="CEO头像" class="profile-image">
                    <div>
                      <h4 class="profile-name">张技术</h4>
                      <p class="profile-position">技术总监 &amp; 创始人</p>
                    </div>
                  </div>
                </div>
                <div class="col-lg-7">
                  <div class="contact-info d-flex align-items-center gap-2">
                    <i class="bi bi-telephone-fill"></i>
                    <div>
                      <p class="contact-label">技术咨询热线</p>
                      <p class="contact-number">+86 138-0000-0000</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="col-xl-6" data-aos="fade-up" data-aos-delay="300">
            <div class="image-wrapper">
              <div class="images position-relative" data-aos="zoom-out" data-aos-delay="400">
                <img src="assets/img/about-5.webp" alt="Java开发" class="img-fluid main-image rounded-4">
                <img src="assets/img/about-2.webp" alt="项目演示" class="img-fluid small-image rounded-4">
              </div>
              <div class="experience-badge floating">
                <h3>500+ <span>个</span></h3>
                <p>成功项目案例</p>
              </div>
            </div>
          </div>
        </div>

      </div>

    </section><!-- /关于我们区域 -->

    <!-- 功能特色区域 -->
    <section id="features" class="features section">

      <!-- 区域标题 -->
      <div class="container section-title" data-aos="fade-up">
        <h2>项目特色</h2>
        <p>我们的Java毕业项目具备以下核心特色，确保您的毕业设计顺利通过</p>
      </div><!-- 区域标题结束 -->

      <div class="container">

        <div class="d-flex justify-content-center">

          <ul class="nav nav-tabs" data-aos="fade-up" data-aos-delay="100">

            <li class="nav-item">
              <a class="nav-link active show" data-bs-toggle="tab" data-bs-target="#features-tab-1">
                <h4>技术先进</h4>
              </a>
            </li><!-- 标签导航项结束 -->

            <li class="nav-item">
              <a class="nav-link" data-bs-toggle="tab" data-bs-target="#features-tab-2">
                <h4>文档完整</h4>
              </a><!-- 标签导航项结束 -->

            </li>
            <li class="nav-item">
              <a class="nav-link" data-bs-toggle="tab" data-bs-target="#features-tab-3">
                <h4>质量保证</h4>
              </a>
            </li><!-- 标签导航项结束 -->

          </ul>

        </div>

        <div class="tab-content" data-aos="fade-up" data-aos-delay="200">

          <div class="tab-pane fade active show" id="features-tab-1">
            <div class="row">
              <div class="col-lg-6 order-2 order-lg-1 mt-3 mt-lg-0 d-flex flex-column justify-content-center">
                <h3>先进技术栈，紧跟时代潮流</h3>
                <p class="fst-italic">
                  我们的Java毕业项目采用最新的技术框架和开发工具，
                  确保项目技术先进性，符合当前行业标准和发展趋势。
                </p>
                <ul>
                  <li><i class="bi bi-check2-all"></i> <span>Spring Boot + MyBatis 主流框架，技术成熟稳定。</span></li>
                  <li><i class="bi bi-check2-all"></i> <span>Vue.js + Element UI 前端技术，界面美观现代。</span></li>
                  <li><i class="bi bi-check2-all"></i> <span>MySQL数据库设计规范，支持高并发访问和数据安全。</span></li>
                </ul>
              </div>
              <div class="col-lg-6 order-1 order-lg-2 text-center">
                <img src="assets/img/features-illustration-1.webp" alt="Java技术栈插图" class="img-fluid">
              </div>
            </div>
          </div><!-- 标签内容项结束 -->

          <div class="tab-pane fade" id="features-tab-2">
            <div class="row">
              <div class="col-lg-6 order-2 order-lg-1 mt-3 mt-lg-0 d-flex flex-column justify-content-center">
                <h3>完整文档资料，答辩无忧</h3>
                <p class="fst-italic">
                  每个项目都配备完整的文档资料和答辩材料，
                  让您轻松应对毕业答辩，顺利完成学业。
                </p>
                <ul>
                  <li><i class="bi bi-check2-all"></i> <span>详细的项目需求分析和系统设计文档。</span></li>
                  <li><i class="bi bi-check2-all"></i> <span>完整的数据库设计文档和SQL脚本文件。</span></li>
                  <li><i class="bi bi-check2-all"></i> <span>精美的PPT答辩演示文稿和演示视频。</span></li>
                  <li><i class="bi bi-check2-all"></i> <span>详细的部署文档和使用说明，确保项目顺利运行。</span></li>
                </ul>
              </div>
              <div class="col-lg-6 order-1 order-lg-2 text-center">
                <img src="assets/img/features-illustration-2.webp" alt="文档资料插图" class="img-fluid">
              </div>
            </div>
          </div><!-- 标签内容项结束 -->

          <div class="tab-pane fade" id="features-tab-3">
            <div class="row">
              <div class="col-lg-6 order-2 order-lg-1 mt-3 mt-lg-0 d-flex flex-column justify-content-center">
                <h3>严格测试，质量保证</h3>
                <ul>
                  <li><i class="bi bi-check2-all"></i> <span>所有项目经过严格测试，确保100%可运行无错误。</span></li>
                  <li><i class="bi bi-check2-all"></i> <span>提供完善的售后技术支持，解答项目相关问题。</span></li>
                  <li><i class="bi bi-check2-all"></i> <span>代码规范整洁，注释详细，易于理解和修改。</span></li>
                </ul>
                <p class="fst-italic">
                  我们承诺为每一位学生提供高质量的毕业项目，
                  通过严格的质量控制确保项目的可靠性和实用性。
                </p>
              </div>
              <div class="col-lg-6 order-1 order-lg-2 text-center">
                <img src="assets/img/features-illustration-3.webp" alt="质量保证插图" class="img-fluid">
              </div>
            </div>
          </div><!-- 标签内容项结束 -->

        </div>

      </div>

    </section><!-- /功能特色区域 -->

    <!-- 功能卡片区域 -->
    <section id="features-cards" class="features-cards section">

      <div class="container">

        <div class="row gy-4">

          <div class="col-xl-3 col-md-6" data-aos="zoom-in" data-aos-delay="100">
            <div class="feature-box orange">
              <i class="bi bi-award"></i>
              <h4>项目丰富</h4>
              <p>涵盖管理系统、电商平台、小程序等多种类型，满足不同专业需求</p>
            </div>
          </div><!-- 功能框结束-->

          <div class="col-xl-3 col-md-6" data-aos="zoom-in" data-aos-delay="200">
            <div class="feature-box blue">
              <i class="bi bi-patch-check"></i>
              <h4>测试完备</h4>
              <p>所有项目经过严格测试，确保功能完整、运行稳定、无错误</p>
            </div>
          </div><!-- 功能框结束-->

          <div class="col-xl-3 col-md-6" data-aos="zoom-in" data-aos-delay="300">
            <div class="feature-box green">
              <i class="bi bi-sunrise"></i>
              <h4>技术支持</h4>
              <p>提供项目部署指导和技术问答，确保您能顺利运行项目</p>
            </div>
          </div><!-- 功能框结束-->

          <div class="col-xl-3 col-md-6" data-aos="zoom-in" data-aos-delay="400">
            <div class="feature-box red">
              <i class="bi bi-shield-check"></i>
              <h4>定制服务</h4>
              <p>支持根据您的具体需求进行项目定制和功能调整</p>
            </div>
          </div><!-- 功能框结束-->

        </div>

      </div>

    </section><!-- /功能卡片区域 -->

    <!-- 功能特色2区域 -->
    <section id="features-2" class="features-2 section">

      <div class="container" data-aos="fade-up" data-aos-delay="100">

        <div class="row align-items-center">

          <div class="col-lg-4">

            <div class="feature-item text-end mb-5" data-aos="fade-right" data-aos-delay="200">
              <div class="d-flex align-items-center justify-content-end gap-4">
                <div class="feature-content">
                  <h3>响应式设计</h3>
                  <p>项目采用响应式设计，完美适配PC端、平板和手机，提供一致的用户体验。</p>
                </div>
                <div class="feature-icon flex-shrink-0">
                  <i class="bi bi-display"></i>
                </div>
              </div>
            </div><!-- 功能项结束 -->

            <div class="feature-item text-end mb-5" data-aos="fade-right" data-aos-delay="300">
              <div class="d-flex align-items-center justify-content-end gap-4">
                <div class="feature-content">
                  <h3>界面美观</h3>
                  <p>采用现代化UI设计，界面简洁美观，用户体验良好，符合当前设计趋势。</p>
                </div>
                <div class="feature-icon flex-shrink-0">
                  <i class="bi bi-feather"></i>
                </div>
              </div>
            </div><!-- 功能项结束 -->

            <div class="feature-item text-end" data-aos="fade-right" data-aos-delay="400">
              <div class="d-flex align-items-center justify-content-end gap-4">
                <div class="feature-content">
                  <h3>功能完整</h3>
                  <p>项目功能模块完整，涵盖用户管理、数据管理、权限控制等核心功能。</p>
                </div>
                <div class="feature-icon flex-shrink-0">
                  <i class="bi bi-eye"></i>
                </div>
              </div>
            </div><!-- 功能项结束 -->

          </div>

          <div class="col-lg-4" data-aos="zoom-in" data-aos-delay="200">
            <div class="phone-mockup text-center">
              <img src="assets/img/phone-app-screen.webp" alt="项目演示界面" class="img-fluid">
            </div>
          </div><!-- 手机模型结束 -->

          <div class="col-lg-4">

            <div class="feature-item mb-5" data-aos="fade-left" data-aos-delay="200">
              <div class="d-flex align-items-center gap-4">
                <div class="feature-icon flex-shrink-0">
                  <i class="bi bi-code-square"></i>
                </div>
                <div class="feature-content">
                  <h3>代码规范</h3>
                  <p>严格遵循Java编码规范，代码结构清晰，注释详细，易于理解和维护。</p>
                </div>
              </div>
            </div><!-- 功能项结束 -->

            <div class="feature-item mb-5" data-aos="fade-left" data-aos-delay="300">
              <div class="d-flex align-items-center gap-4">
                <div class="feature-icon flex-shrink-0">
                  <i class="bi bi-phone"></i>
                </div>
                <div class="feature-content">
                  <h3>多端适配</h3>
                  <p>支持PC端和移动端访问，采用响应式布局，在各种设备上都能完美运行。</p>
                </div>
              </div>
            </div><!-- 功能项结束 -->

            <div class="feature-item" data-aos="fade-left" data-aos-delay="400">
              <div class="d-flex align-items-center gap-4">
                <div class="feature-icon flex-shrink-0">
                  <i class="bi bi-browser-chrome"></i>
                </div>
                <div class="feature-content">
                  <h3>兼容性强</h3>
                  <p>全面兼容主流浏览器和操作系统，确保项目在不同环境下都能稳定运行。</p>
                </div>
              </div>
            </div><!-- 功能项结束 -->

          </div>
        </div>

      </div>

    </section><!-- /功能特色2区域 -->

    <!-- 行动号召区域 -->
    <section id="call-to-action" class="call-to-action section">

      <div class="container" data-aos="fade-up" data-aos-delay="100">

        <div class="row content justify-content-center align-items-center position-relative">
          <div class="col-lg-8 mx-auto text-center">
            <h2 class="display-4 mb-4">准备好选择您的毕业项目了吗？</h2>
            <p class="mb-4">我们有500+优质Java毕业项目等您选择。每个项目都经过严格测试，配套完整文档，助您顺利毕业。</p>
            <a href="#" class="btn btn-cta">立即选购</a>
          </div>

          <!-- Abstract Background Elements -->
          <div class="shape shape-1">
            <svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
              <path d="M47.1,-57.1C59.9,-45.6,68.5,-28.9,71.4,-10.9C74.2,7.1,71.3,26.3,61.5,41.1C51.7,55.9,35,66.2,16.9,69.2C-1.3,72.2,-21,67.8,-36.9,57.9C-52.8,48,-64.9,32.6,-69.1,15.1C-73.3,-2.4,-69.5,-22,-59.4,-37.1C-49.3,-52.2,-32.8,-62.9,-15.7,-64.9C1.5,-67,34.3,-68.5,47.1,-57.1Z" transform="translate(100 100)"></path>
            </svg>
          </div>

          <div class="shape shape-2">
            <svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
              <path d="M41.3,-49.1C54.4,-39.3,66.6,-27.2,71.1,-12.1C75.6,3,72.4,20.9,63.3,34.4C54.2,47.9,39.2,56.9,23.2,62.3C7.1,67.7,-10,69.4,-24.8,64.1C-39.7,58.8,-52.3,46.5,-60.1,31.5C-67.9,16.4,-70.9,-1.4,-66.3,-16.6C-61.8,-31.8,-49.7,-44.3,-36.3,-54C-22.9,-63.7,-8.2,-70.6,3.6,-75.1C15.4,-79.6,28.2,-58.9,41.3,-49.1Z" transform="translate(100 100)"></path>
            </svg>
          </div>

          <!-- Dot Pattern Groups -->
          <div class="dots dots-1">
            <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
              <pattern id="dot-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
                <circle cx="2" cy="2" r="2" fill="currentColor"></circle>
              </pattern>
              <rect width="100" height="100" fill="url(#dot-pattern)"></rect>
            </svg>
          </div>

          <div class="dots dots-2">
            <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
              <pattern id="dot-pattern-2" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
                <circle cx="2" cy="2" r="2" fill="currentColor"></circle>
              </pattern>
              <rect width="100" height="100" fill="url(#dot-pattern-2)"></rect>
            </svg>
          </div>

          <div class="shape shape-3">
            <svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
              <path d="M43.3,-57.1C57.4,-46.5,71.1,-32.6,75.3,-16.2C79.5,0.2,74.2,19.1,65.1,35.3C56,51.5,43.1,65,27.4,71.7C11.7,78.4,-6.8,78.3,-23.9,72.4C-41,66.5,-56.7,54.8,-65.4,39.2C-74.1,23.6,-75.8,4,-71.7,-13.2C-67.6,-30.4,-57.7,-45.2,-44.3,-56.1C-30.9,-67,-15.5,-74,0.7,-74.9C16.8,-75.8,33.7,-70.7,43.3,-57.1Z" transform="translate(100 100)"></path>
            </svg>
          </div>
        </div>

      </div>

    </section><!-- /行动号召区域 -->

    <!-- 客户展示区域 -->
    <section id="clients" class="clients section">

      <div class="container" data-aos="fade-up" data-aos-delay="100">

        <div class="swiper init-swiper">
          <script type="application/json" class="swiper-config">
            {
              "loop": true,
              "speed": 600,
              "autoplay": {
                "delay": 5000
              },
              "slidesPerView": "auto",
              "pagination": {
                "el": ".swiper-pagination",
                "type": "bullets",
                "clickable": true
              },
              "breakpoints": {
                "320": {
                  "slidesPerView": 2,
                  "spaceBetween": 40
                },
                "480": {
                  "slidesPerView": 3,
                  "spaceBetween": 60
                },
                "640": {
                  "slidesPerView": 4,
                  "spaceBetween": 80
                },
                "992": {
                  "slidesPerView": 6,
                  "spaceBetween": 120
                }
              }
            }
          </script>
          <div class="swiper-wrapper align-items-center">
            <div class="swiper-slide"><img src="assets/img/clients/client-1.png" class="img-fluid" alt="客户1"></div>
            <div class="swiper-slide"><img src="assets/img/clients/client-2.png" class="img-fluid" alt="客户2"></div>
            <div class="swiper-slide"><img src="assets/img/clients/client-3.png" class="img-fluid" alt="客户3"></div>
            <div class="swiper-slide"><img src="assets/img/clients/client-4.png" class="img-fluid" alt="客户4"></div>
            <div class="swiper-slide"><img src="assets/img/clients/client-5.png" class="img-fluid" alt="客户5"></div>
            <div class="swiper-slide"><img src="assets/img/clients/client-6.png" class="img-fluid" alt="客户6"></div>
            <div class="swiper-slide"><img src="assets/img/clients/client-7.png" class="img-fluid" alt="客户7"></div>
            <div class="swiper-slide"><img src="assets/img/clients/client-8.png" class="img-fluid" alt="客户8"></div>
          </div>
          <div class="swiper-pagination"></div>
        </div>

      </div>

    </section><!-- /客户展示区域 -->

    <!-- 客户评价区域 -->
    <section id="testimonials" class="testimonials section light-background">

      <!-- 区域标题 -->
      <div class="container section-title" data-aos="fade-up">
        <h2>学生评价</h2>
        <p>听听购买过我们项目的学生对项目质量和服务的真实评价</p>
      </div><!-- 区域标题结束 -->

      <div class="container">

        <div class="row g-5">

          <div class="col-lg-6" data-aos="fade-up" data-aos-delay="100">
            <div class="testimonial-item">
              <img src="assets/img/testimonials/testimonials-1.jpg" class="testimonial-img" alt="学生评价1">
              <h3>李同学</h3>
              <h4>计算机科学与技术专业</h4>
              <div class="stars">
                <i class="bi bi-star-fill"></i><i class="bi bi-star-fill"></i><i class="bi bi-star-fill"></i><i class="bi bi-star-fill"></i><i class="bi bi-star-fill"></i>
              </div>
              <p>
                <i class="bi bi-quote quote-icon-left"></i>
                <span>项目质量非常高，代码规范，文档详细。最重要的是真的能100%运行，答辩时老师对项目很满意，顺利通过了毕业答辩。强烈推荐给需要毕业项目的同学！</span>
                <i class="bi bi-quote quote-icon-right"></i>
              </p>
            </div>
          </div><!-- 评价项结束 -->

          <div class="col-lg-6" data-aos="fade-up" data-aos-delay="200">
            <div class="testimonial-item">
              <img src="assets/img/testimonials/testimonials-2.jpg" class="testimonial-img" alt="学生评价2">
              <h3>王同学</h3>
              <h4>软件工程专业</h4>
              <div class="stars">
                <i class="bi bi-star-fill"></i><i class="bi bi-star-fill"></i><i class="bi bi-star-fill"></i><i class="bi bi-star-fill"></i><i class="bi bi-star-fill"></i>
              </div>
              <p>
                <i class="bi bi-quote quote-icon-left"></i>
                <span>购买体验非常好，客服很耐心地解答了我的问题。项目文档特别详细，部署很顺利。技术支持也很及时，遇到问题都能快速解决。真的帮我节省了很多时间。</span>
                <i class="bi bi-quote quote-icon-right"></i>
              </p>
            </div>
          </div><!-- 评价项结束 -->

          <div class="col-lg-6" data-aos="fade-up" data-aos-delay="300">
            <div class="testimonial-item">
              <img src="assets/img/testimonials/testimonials-3.jpg" class="testimonial-img" alt="学生评价3">
              <h3>张同学</h3>
              <h4>信息管理与信息系统专业</h4>
              <div class="stars">
                <i class="bi bi-star-fill"></i><i class="bi bi-star-fill"></i><i class="bi bi-star-fill"></i><i class="bi bi-star-fill"></i><i class="bi bi-star-fill"></i>
              </div>
              <p>
                <i class="bi bi-quote quote-icon-left"></i>
                <span>选择的电商管理系统项目非常棒！功能完整，界面美观，老师和同学都很认可。PPT模板也很专业，答辩时表现很好。感谢马到码成团队的优质服务！</span>
                <i class="bi bi-quote quote-icon-right"></i>
              </p>
            </div>
          </div><!-- 评价项结束 -->

          <div class="col-lg-6" data-aos="fade-up" data-aos-delay="400">
            <div class="testimonial-item">
              <img src="assets/img/testimonials/testimonials-4.jpg" class="testimonial-img" alt="学生评价4">
              <h3>刘同学</h3>
              <h4>电子商务专业</h4>
              <div class="stars">
                <i class="bi bi-star-fill"></i><i class="bi bi-star-fill"></i><i class="bi bi-star-fill"></i><i class="bi bi-star-fill"></i><i class="bi bi-star-fill"></i>
              </div>
              <p>
                <i class="bi bi-quote quote-icon-left"></i>
                <span>作为非计算机专业的学生，我很担心毕业设计。但是马到码成的项目让我很放心，不仅代码质量高，而且有详细的说明文档，让我能够理解项目的每个部分。</span>
                <i class="bi bi-quote quote-icon-right"></i>
              </p>
            </div>
          </div><!-- 评价项结束 -->

        </div>

      </div>

    </section><!-- /客户评价区域 -->

    <!-- 统计数据区域 -->
    <section id="stats" class="stats section">

      <div class="container" data-aos="fade-up" data-aos-delay="100">

        <div class="row gy-4">

          <div class="col-lg-3 col-md-6">
            <div class="stats-item text-center w-100 h-100">
              <span data-purecounter-start="0" data-purecounter-end="12000" data-purecounter-duration="1" class="purecounter"></span>
              <p>成功毕业学生</p>
            </div>
          </div><!-- 统计项结束 -->

          <div class="col-lg-3 col-md-6">
            <div class="stats-item text-center w-100 h-100">
              <span data-purecounter-start="0" data-purecounter-end="500" data-purecounter-duration="1" class="purecounter"></span>
              <p>项目案例</p>
            </div>
          </div><!-- 统计项结束 -->

          <div class="col-lg-3 col-md-6">
            <div class="stats-item text-center w-100 h-100">
              <span data-purecounter-start="0" data-purecounter-end="24" data-purecounter-duration="1" class="purecounter"></span>
              <p>小时技术支持</p>
            </div>
          </div><!-- 统计项结束 -->

          <div class="col-lg-3 col-md-6">
            <div class="stats-item text-center w-100 h-100">
              <span data-purecounter-start="0" data-purecounter-end="98" data-purecounter-duration="1" class="purecounter"></span>
              <p>%毕业成功率</p>
            </div>
          </div><!-- 统计项结束 -->

        </div>

      </div>

    </section><!-- /统计数据区域 -->

    <!-- 服务项目区域 -->
    <section id="services" class="services section light-background">

      <!-- 区域标题 -->
      <div class="container section-title" data-aos="fade-up">
        <h2>项目分类</h2>
        <p>我们提供多种类型的Java毕业项目，涵盖各个专业方向和技术领域</p>
      </div><!-- 区域标题结束 -->

      <div class="container" data-aos="fade-up" data-aos-delay="100">

        <div class="row g-4">

          <div class="col-lg-6" data-aos="fade-up" data-aos-delay="100">
            <div class="service-card d-flex">
              <div class="icon flex-shrink-0">
                <i class="bi bi-activity"></i>
              </div>
              <div>
                <h3>管理系统类</h3>
                <p>包括学生管理系统、图书管理系统、人事管理系统等，功能完整，适合各种管理类毕业设计需求。</p>
                <a href="#" class="read-more">查看项目 <i class="bi bi-arrow-right"></i></a>
              </div>
            </div>
          </div><!-- 服务卡片结束 -->

          <div class="col-lg-6" data-aos="fade-up" data-aos-delay="200">
            <div class="service-card d-flex">
              <div class="icon flex-shrink-0">
                <i class="bi bi-diagram-3"></i>
              </div>
              <div>
                <h3>电商平台类</h3>
                <p>涵盖在线商城、购物车系统、支付系统等电商核心功能，技术先进，界面美观。</p>
                <a href="#" class="read-more">查看项目 <i class="bi bi-arrow-right"></i></a>
              </div>
            </div>
          </div><!-- 服务卡片结束 -->

          <div class="col-lg-6" data-aos="fade-up" data-aos-delay="300">
            <div class="service-card d-flex">
              <div class="icon flex-shrink-0">
                <i class="bi bi-easel"></i>
              </div>
              <div>
                <h3>小程序项目</h3>
                <p>微信小程序、支付宝小程序等移动端项目，紧跟技术潮流，满足移动互联网时代需求。</p>
                <a href="#" class="read-more">查看项目 <i class="bi bi-arrow-right"></i></a>
              </div>
            </div>
          </div><!-- 服务卡片结束 -->

          <div class="col-lg-6" data-aos="fade-up" data-aos-delay="400">
            <div class="service-card d-flex">
              <div class="icon flex-shrink-0">
                <i class="bi bi-clipboard-data"></i>
              </div>
              <div>
                <h3>Web应用类</h3>
                <p>基于Spring Boot的Web应用项目，包括博客系统、论坛系统、在线考试系统等。</p>
                <a href="#" class="read-more">查看项目 <i class="bi bi-arrow-right"></i></a>
              </div>
            </div>
          </div><!-- 服务卡片结束 -->

        </div>

      </div>

    </section><!-- /服务项目区域 -->

    <!-- 价格方案区域 -->
    <section id="pricing" class="pricing section light-background">

      <!-- 区域标题 -->
      <div class="container section-title" data-aos="fade-up">
        <h2>价格方案</h2>
        <p>根据项目复杂度和功能需求，我们提供不同价位的毕业项目选择</p>
      </div><!-- 区域标题结束 -->

      <div class="container" data-aos="fade-up" data-aos-delay="100">

        <div class="row g-4 justify-content-center">

          <!-- 基础方案 -->
          <div class="col-lg-4" data-aos="fade-up" data-aos-delay="100">
            <div class="pricing-card">
              <h3>基础项目</h3>
              <div class="price">
                <span class="currency">¥</span>
                <span class="amount">199</span>
                <span class="period">/ 个</span>
              </div>
              <p class="description">适合功能相对简单的毕业设计，包含基础的CRUD操作和用户管理功能。</p>

              <h4>包含内容：</h4>
              <ul class="features-list">
                <li>
                  <i class="bi bi-check-circle-fill"></i>
                  完整源代码
                </li>
                <li>
                  <i class="bi bi-check-circle-fill"></i>
                  数据库文件
                </li>
                <li>
                  <i class="bi bi-check-circle-fill"></i>
                  基础文档
                </li>
              </ul>

              <a href="#" class="btn btn-primary">
                立即购买
                <i class="bi bi-arrow-right"></i>
              </a>
            </div>
          </div>

          <!-- 标准方案 -->
          <div class="col-lg-4" data-aos="fade-up" data-aos-delay="200">
            <div class="pricing-card popular">
              <div class="popular-badge">最受欢迎</div>
              <h3>标准项目</h3>
              <div class="price">
                <span class="currency">¥</span>
                <span class="amount">399</span>
                <span class="period">/ 个</span>
              </div>
              <p class="description">功能完整的毕业项目，包含完整的业务流程和丰富的功能模块。</p>

              <h4>包含内容：</h4>
              <ul class="features-list">
                <li>
                  <i class="bi bi-check-circle-fill"></i>
                  完整源代码
                </li>
                <li>
                  <i class="bi bi-check-circle-fill"></i>
                  详细设计文档
                </li>
                <li>
                  <i class="bi bi-check-circle-fill"></i>
                  PPT答辩模板
                </li>
                <li>
                  <i class="bi bi-check-circle-fill"></i>
                  技术支持
                </li>
              </ul>

              <a href="#" class="btn btn-light">
                立即购买
                <i class="bi bi-arrow-right"></i>
              </a>
            </div>
          </div>

          <!-- 高级方案 -->
          <div class="col-lg-4" data-aos="fade-up" data-aos-delay="300">
            <div class="pricing-card">
              <h3>高级项目</h3>
              <div class="price">
                <span class="currency">¥</span>
                <span class="amount">699</span>
                <span class="period">/ 个</span>
              </div>
              <p class="description">复杂度高的大型项目，技术先进，功能丰富，适合要求较高的毕业设计。</p>

              <h4>包含内容：</h4>
              <ul class="features-list">
                <li>
                  <i class="bi bi-check-circle-fill"></i>
                  完整源代码
                </li>
                <li>
                  <i class="bi bi-check-circle-fill"></i>
                  全套设计文档
                </li>
                <li>
                  <i class="bi bi-check-circle-fill"></i>
                  精美PPT模板
                </li>
                <li>
                  <i class="bi bi-check-circle-fill"></i>
                  演示视频
                </li>
                <li>
                  <i class="bi bi-check-circle-fill"></i>
                  一对一技术指导
                </li>
              </ul>

              <a href="#" class="btn btn-primary">
                立即购买
                <i class="bi bi-arrow-right"></i>
              </a>
            </div>
          </div>

        </div>

      </div>

    </section><!-- /价格方案区域 -->

    <!-- 常见问题区域 -->
    <section class="faq-9 faq section light-background" id="faq">

      <div class="container">
        <div class="row">

          <div class="col-lg-5" data-aos="fade-up">
            <h2 class="faq-title">有疑问？查看常见问题</h2>
            <p class="faq-description">我们整理了学生最常询问的问题和详细解答，帮助您更好地了解我们的项目和服务。</p>
            <div class="faq-arrow d-none d-lg-block" data-aos="fade-up" data-aos-delay="200">
              <svg class="faq-arrow" width="200" height="211" viewBox="0 0 200 211" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M198.804 194.488C189.279 189.596 179.529 185.52 169.407 182.07L169.384 182.049C169.227 181.994 169.07 181.939 168.912 181.884C166.669 181.139 165.906 184.546 167.669 185.615C174.053 189.473 182.761 191.837 189.146 195.695C156.603 195.912 119.781 196.591 91.266 179.049C62.5221 161.368 48.1094 130.695 56.934 98.891C84.5539 98.7247 112.556 84.0176 129.508 62.667C136.396 53.9724 146.193 35.1448 129.773 30.2717C114.292 25.6624 93.7109 41.8875 83.1971 51.3147C70.1109 63.039 59.63 78.433 54.2039 95.0087C52.1221 94.9842 50.0776 94.8683 48.0703 94.6608C30.1803 92.8027 11.2197 83.6338 5.44902 65.1074C-1.88449 41.5699 14.4994 19.0183 27.9202 1.56641C28.6411 0.625793 27.2862 -0.561638 26.5419 0.358501C13.4588 16.4098 -0.221091 34.5242 0.896608 56.5659C1.8218 74.6941 14.221 87.9401 30.4121 94.2058C37.7076 97.0203 45.3454 98.5003 53.0334 98.8449C47.8679 117.532 49.2961 137.487 60.7729 155.283C87.7615 197.081 139.616 201.147 184.786 201.155L174.332 206.827C172.119 208.033 174.345 211.287 176.537 210.105C182.06 207.125 187.582 204.122 193.084 201.144C193.346 201.147 195.161 199.887 195.423 199.868C197.08 198.548 193.084 201.144 195.528 199.81C196.688 199.192 197.846 198.552 199.006 197.935C200.397 197.167 200.007 195.087 198.804 194.488ZM60.8213 88.0427C67.6894 72.648 78.8538 59.1566 92.1207 49.0388C98.8475 43.9065 106.334 39.2953 114.188 36.1439C117.295 34.8947 120.798 33.6609 124.168 33.635C134.365 33.5511 136.354 42.9911 132.638 51.031C120.47 77.4222 86.8639 93.9837 58.0983 94.9666C58.8971 92.6666 59.783 90.3603 60.8213 88.0427Z" fill="currentColor"></path>
              </svg>
            </div>
          </div>

          <div class="col-lg-7" data-aos="fade-up" data-aos-delay="300">
            <div class="faq-container">

              <div class="faq-item faq-active">
                <h3>项目真的能100%运行吗？</h3>
                <div class="faq-content">
                  <p>是的，我们承诺所有项目都经过严格测试，确保100%可运行。每个项目都配有详细的部署文档和数据库文件，按照说明操作即可成功运行。</p>
                </div>
                <i class="faq-toggle bi bi-chevron-right"></i>
              </div><!-- 常见问题项结束-->

              <div class="faq-item">
                <h3>购买后多久能收到项目文件？</h3>
                <div class="faq-content">
                  <p>付款成功后，我们会在2小时内将完整的项目文件发送到您的邮箱，包括源代码、数据库文件、文档资料等。如有特殊情况会提前告知。</p>
                </div>
                <i class="faq-toggle bi bi-chevron-right"></i>
              </div><!-- 常见问题项结束-->

              <div class="faq-item">
                <h3>提供技术支持和答疑服务吗？</h3>
                <div class="faq-content">
                  <p>是的，我们提供完善的技术支持服务。购买后可以通过QQ、微信等方式联系我们，我们会及时解答项目部署、运行等相关问题。</p>
                </div>
                <i class="faq-toggle bi bi-chevron-right"></i>
              </div><!-- 常见问题项结束-->

              <div class="faq-item">
                <h3>项目的技术栈是什么？</h3>
                <div class="faq-content">
                  <p>我们的项目主要采用Java技术栈，包括Spring Boot、MyBatis、MySQL、Vue.js等主流技术。所有技术都是当前企业广泛使用的，具有很好的学习和实用价值。</p>
                </div>
                <i class="faq-toggle bi bi-chevron-right"></i>
              </div><!-- 常见问题项结束-->

              <div class="faq-item">
                <h3>可以根据我的需求定制项目吗？</h3>
                <div class="faq-content">
                  <p>可以的。我们提供项目定制服务，可以根据您的专业要求、功能需求等进行个性化定制。定制项目的价格会根据具体需求和工作量来确定。</p>
                </div>
                <i class="faq-toggle bi bi-chevron-right"></i>
              </div><!-- 常见问题项结束-->

              <div class="faq-item">
                <h3>如何保证项目的原创性？</h3>
                <div class="faq-content">
                  <p>我们所有项目都是团队原创开发，不存在抄袭问题。每个项目都有独特的设计思路和实现方案，确保您的毕业设计具有原创性和独特性。</p>
                </div>
                <i class="faq-toggle bi bi-chevron-right"></i>
              </div><!-- 常见问题项结束-->

            </div>
          </div>

        </div>
      </div>
    </section><!-- /常见问题区域 -->

    <!-- 行动号召2区域 -->
    <section id="call-to-action-2" class="call-to-action-2 section dark-background">

      <div class="container">
        <div class="row justify-content-center" data-aos="zoom-in" data-aos-delay="100">
          <div class="col-xl-10">
            <div class="text-center">
              <h3>马到码成</h3>
              <p>不要让毕业设计成为您的烦恼！选择马到码成，选择成功毕业。我们有500+优质项目等您选择，助您轻松完成毕业设计，顺利走向职场。</p>
              <a class="cta-btn" href="#">立即选购</a>
            </div>
          </div>
        </div>
      </div>

    </section><!-- /行动号召2区域 -->

    <!-- 联系我们区域 -->
    <section id="contact" class="contact section light-background">

      <!-- 区域标题 -->
      <div class="container section-title" data-aos="fade-up">
        <h2>联系我们</h2>
        <p>有任何关于毕业项目的问题，欢迎随时联系我们，我们将为您提供专业的咨询和技术支持</p>
      </div><!-- 区域标题结束 -->

      <div class="container" data-aos="fade-up" data-aos-delay="100">

        <div class="row g-4 g-lg-5">
          <div class="col-lg-5">
            <div class="info-box" data-aos="fade-up" data-aos-delay="200">
              <h3>联系信息</h3>
              <p>马到码成团队随时为您提供毕业项目咨询和技术支持。请通过以下方式与我们联系，我们会及时回复。</p>

              <div class="info-item" data-aos="fade-up" data-aos-delay="300">
                <div class="icon-box">
                  <i class="bi bi-geo-alt"></i>
                </div>
                <div class="content">
                  <h4>服务地区</h4>
                  <p>全国各地高校学生</p>
                  <p>在线服务，无地域限制</p>
                </div>
              </div>

              <div class="info-item" data-aos="fade-up" data-aos-delay="400">
                <div class="icon-box">
                  <i class="bi bi-telephone"></i>
                </div>
                <div class="content">
                  <h4>咨询热线</h4>
                  <p>+86 138-0000-0000</p>
                  <p>工作时间：9:00-22:00</p>
                </div>
              </div>

              <div class="info-item" data-aos="fade-up" data-aos-delay="500">
                <div class="icon-box">
                  <i class="bi bi-envelope"></i>
                </div>
                <div class="content">
                  <h4>邮箱联系</h4>
                  <p><EMAIL></p>
                  <p><EMAIL></p>
                </div>
              </div>
            </div>
          </div>

          <div class="col-lg-7">
            <div class="contact-form" data-aos="fade-up" data-aos-delay="300">
              <h3>项目咨询</h3>
              <p>请填写以下表单告诉我们您的需求，我们会为您推荐最适合的毕业项目。</p>

              <form action="forms/contact.php" method="post" class="php-email-form" data-aos="fade-up" data-aos-delay="200">
                <div class="row gy-4">

                  <div class="col-md-6">
                    <input type="text" name="name" class="form-control" placeholder="您的姓名" required="">
                  </div>

                  <div class="col-md-6 ">
                    <input type="email" class="form-control" name="email" placeholder="您的邮箱" required="">
                  </div>

                  <div class="col-12">
                    <input type="text" class="form-control" name="subject" placeholder="专业/项目类型需求" required="">
                  </div>

                  <div class="col-12">
                    <textarea class="form-control" name="message" rows="6" placeholder="请描述您的毕业项目需求，包括专业、功能要求、技术偏好等" required=""></textarea>
                  </div>

                  <div class="col-12 text-center">
                    <div class="loading">正在发送</div>
                    <div class="error-message"></div>
                    <div class="sent-message">您的咨询已发送成功，我们会尽快回复！</div>

                    <button type="submit" class="btn">提交咨询</button>
                  </div>

                </div>
              </form>

            </div>
          </div>

        </div>

      </div>

    </section><!-- /联系我们区域 -->

  </main>

  <footer id="footer" class="footer">

    <div class="container footer-top">
      <div class="row gy-4">
        <div class="col-lg-4 col-md-6 footer-about">
          <a href="index.html" class="logo d-flex align-items-center">
            <span class="sitename">马到码成</span>
          </a>
          <div class="footer-contact pt-3">
            <p>专业Java毕业项目源代码销售</p>
            <p>全国高校学生服务</p>
            <p class="mt-3"><strong>咨询热线:</strong> <span>+86 138-0000-0000</span></p>
            <p><strong>邮箱:</strong> <span><EMAIL></span></p>
          </div>
          <div class="social-links d-flex mt-4">
            <a href=""><i class="bi bi-twitter-x"></i></a>
            <a href=""><i class="bi bi-facebook"></i></a>
            <a href=""><i class="bi bi-instagram"></i></a>
            <a href=""><i class="bi bi-linkedin"></i></a>
          </div>
        </div>

        <div class="col-lg-2 col-md-3 footer-links">
          <h4>快速链接</h4>
          <ul>
            <li><a href="#">首页</a></li>
            <li><a href="#">关于我们</a></li>
            <li><a href="#">项目分类</a></li>
            <li><a href="#">服务条款</a></li>
            <li><a href="#">隐私政策</a></li>
          </ul>
        </div>

        <div class="col-lg-2 col-md-3 footer-links">
          <h4>项目分类</h4>
          <ul>
            <li><a href="#">管理系统</a></li>
            <li><a href="#">电商平台</a></li>
            <li><a href="#">小程序项目</a></li>
            <li><a href="#">Web应用</a></li>
            <li><a href="#">移动应用</a></li>
          </ul>
        </div>

        <div class="col-lg-2 col-md-3 footer-links">
          <h4>技术栈</h4>
          <ul>
            <li><a href="#">Spring Boot</a></li>
            <li><a href="#">MyBatis</a></li>
            <li><a href="#">Vue.js</a></li>
            <li><a href="#">MySQL</a></li>
            <li><a href="#">微信小程序</a></li>
          </ul>
        </div>

        <div class="col-lg-2 col-md-3 footer-links">
          <h4>服务支持</h4>
          <ul>
            <li><a href="#">项目演示</a></li>
            <li><a href="#">技术支持</a></li>
            <li><a href="#">在线咨询</a></li>
            <li><a href="#">定制服务</a></li>
            <li><a href="#">售后保障</a></li>
          </ul>
        </div>

      </div>
    </div>

    <div class="container copyright text-center mt-4">
      <p>© <span>版权所有</span> <strong class="px-1 sitename">马到码成</strong> <span>保留所有权利</span></p>
      <div class="credits">
        <!-- 页脚中的所有链接都应保持完整。 -->
        <!-- 只有购买了专业版本才能删除这些链接。 -->
        <!-- 许可信息: https://bootstrapmade.com/license/ -->
        <!-- 购买带有PHP/AJAX联系表单的专业版本: [buy-url] -->
        设计者 <a href="https://bootstrapmade.com/">BootstrapMade</a> 分发者 <a href="https://themewagon.com">ThemeWagon</a>
      </div>
    </div>

  </footer>

  <!-- 回到顶部 -->
  <a href="#" id="scroll-top" class="scroll-top d-flex align-items-center justify-content-center"><i class="bi bi-arrow-up-short"></i></a>

  <!-- Vendor JS Files -->
  <script src="assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
  <script src="assets/vendor/php-email-form/validate.js"></script>
  <script src="assets/vendor/aos/aos.js"></script>
  <script src="assets/vendor/glightbox/js/glightbox.min.js"></script>
  <script src="assets/vendor/swiper/swiper-bundle.min.js"></script>
  <script src="assets/vendor/purecounter/purecounter_vanilla.js"></script>

  <!-- Main JS File -->
  <script src="assets/js/main.js"></script>

</body>

</html>